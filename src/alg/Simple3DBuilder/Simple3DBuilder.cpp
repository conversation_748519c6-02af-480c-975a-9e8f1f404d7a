#include "Simple3DBuilder.h"

#include <pcl/io/ply_io.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>

#include <BRepAdaptor_Curve.hxx>
#include <BRepAlgoAPI_Cut.hxx>
#include <BRepAlgoAPI_Fuse.hxx>
#include <BRepBuilderAPI_Transform.hxx>
#include <BRepFilletAPI_MakeFillet.hxx>
#include <BRepMesh_IncrementalMesh.hxx>
#include <BRepPrimAPI_MakeBox.hxx>
#include <BRepPrimAPI_MakeCone.hxx>
#include <BRepPrimAPI_MakeCylinder.hxx>
#include <BRepTools.hxx>
#include <BRep_Tool.hxx>
#include <Eigen/Dense>
#include <GCPnts_AbscissaPoint.hxx>
#include <Geom_Curve.hxx>
#include <Poly_Array1OfTriangle.hxx>
#include <Poly_Triangle.hxx>
#include <Poly_Triangulation.hxx>
#include <StlAPI_Reader.hxx>
#include <StlAPI_Writer.hxx>
#include <TopExp_Explorer.hxx>
#include <TopLoc_Location.hxx>
#include <TopoDS.hxx>
#include <TopoDS_Face.hxx>
#include <TopoDS_Shape.hxx>
#include <TopoDS_Solid.hxx>
#include <cmath>
#include <filesystem>
#include <fstream>
#include <gp_Pnt.hxx>
#include <stdexcept>
#include <string>
#include <vector>
#include <set>
#include <limits>
#include <algorithm>
#include <thread>
#include <mutex>
#include <atomic>
#include <random>
#include <chrono>

Eigen::Vector3f sample_point_on_triangle(const Eigen::Vector3f& A, const Eigen::Vector3f& B, const Eigen::Vector3f& C) {
  float u = static_cast<float>(rand()) / RAND_MAX;
  float v = static_cast<float>(rand()) / RAND_MAX;
  if (u + v > 1.0f) {
    u = 1.0f - u;
    v = 1.0f - v;
  }
  float w = 1.0f - u - v;
  return u * A + v * B + w * C;
}

// 计算点到点的距离
float point_distance(const Eigen::Vector3f& p1, const Eigen::Vector3f& p2) {
  return (p1 - p2).norm();
}

void Simple3DBuilder::sample_shape_surface_to_ply(const TopoDS_Shape& shape, const std::string& ply_path, int target_total_points) {
  std::vector<std::tuple<Eigen::Vector3f, Eigen::Vector3f, Eigen::Vector3f, float>> triangles;
  float total_area = 0.0f;

  // 1. 遍历所有面并提取三角形
  for (TopExp_Explorer faceExp(shape, TopAbs_FACE); faceExp.More(); faceExp.Next()) {
    TopoDS_Face face = TopoDS::Face(faceExp.Current());
    TopLoc_Location loc;
    Handle(Poly_Triangulation) triangulation = BRep_Tool::Triangulation(face, loc);
    if (triangulation.IsNull()) continue;

    for (int i = 1; i <= triangulation->NbTriangles(); ++i) {
      Poly_Triangle tri = triangulation->Triangle(i);
      int n1, n2, n3;
      tri.Get(n1, n2, n3);

      if (n1 < 1 || n1 > triangulation->NbNodes() ||
          n2 < 1 || n2 > triangulation->NbNodes() ||
          n3 < 1 || n3 > triangulation->NbNodes()) {
        std::cerr << "三角形索引越界，跳过。" << std::endl;
        continue;
      }

      gp_Pnt p1 = triangulation->Node(n1).Transformed(loc.Transformation());
      gp_Pnt p2 = triangulation->Node(n2).Transformed(loc.Transformation());
      gp_Pnt p3 = triangulation->Node(n3).Transformed(loc.Transformation());

      Eigen::Vector3f v1(p1.X(), p1.Y(), p1.Z());
      Eigen::Vector3f v2(p2.X(), p2.Y(), p2.Z());
      Eigen::Vector3f v3(p3.X(), p3.Y(), p3.Z());

      // 计算面积
      float area = 0.5f * ((v2 - v1).cross(v3 - v1)).norm();
      total_area += area;
      triangles.emplace_back(v1, v2, v3, area);
    }
  }

  // 2. 面积加权采样
  pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>());

  // 使用多线程并行采样
  unsigned int num_threads = std::min(std::thread::hardware_concurrency(), 8u);
  if (num_threads == 0) num_threads = 4;

  // 计算每个三角形的采样点数
  std::vector<int> int_samples(triangles.size(), 0);
  int total_samples = 0;

  // 直接计算整数采样点数，避免浮点运算
  for (size_t i = 0; i < triangles.size(); ++i) {
    float area_ratio = std::get<3>(triangles[i]) / total_area;
    int samples = static_cast<int>(target_total_points * area_ratio);
    int_samples[i] = samples;
    total_samples += samples;
  }

  // 简单分配剩余点数给最大的三角形
  int remaining_to_assign = target_total_points - total_samples;
  if (remaining_to_assign > 0) {
    // 找到面积最大的几个三角形来分配剩余点数
    std::vector<std::pair<size_t, float>> area_indices;
    for (size_t i = 0; i < triangles.size(); ++i) {
      area_indices.emplace_back(i, std::get<3>(triangles[i]));
    }
    std::sort(area_indices.begin(), area_indices.end(),
              [](const auto& a, const auto& b) { return a.second > b.second; });

    for (int i = 0; i < remaining_to_assign && i < static_cast<int>(area_indices.size()); ++i) {
      int_samples[area_indices[i].first]++;
    }
  }

  // 多线程并行采样
  std::vector<std::thread> threads;
  std::mutex cloud_mutex;

  auto worker = [&](size_t start_idx, size_t end_idx) {
    std::vector<pcl::PointXYZ> local_points;
    local_points.reserve((end_idx - start_idx) * 10); // 预分配空间

    for (size_t i = start_idx; i < end_idx; ++i) {
      const auto& [v1, v2, v3, area] = triangles[i];
      for (int j = 0; j < int_samples[i]; ++j) {
        Eigen::Vector3f pt = sample_point_on_triangle(v1, v2, v3);
        local_points.emplace_back(pt.x(), pt.y(), pt.z());
      }
    }

    // 批量添加到主点云
    std::lock_guard<std::mutex> lock(cloud_mutex);
    cloud->points.insert(cloud->points.end(), local_points.begin(), local_points.end());
  };

  // 分配工作给线程
  size_t triangles_per_thread = triangles.size() / num_threads;
  for (unsigned int t = 0; t < num_threads; ++t) {
    size_t start_idx = t * triangles_per_thread;
    size_t end_idx = (t == num_threads - 1) ? triangles.size() : (t + 1) * triangles_per_thread;
    threads.emplace_back(worker, start_idx, end_idx);
  }

  // 等待所有线程完成
  for (auto& thread : threads) {
    thread.join();
  }

  // 3. 更新最终点云属性
  cloud->width = cloud->points.size();
  cloud->height = 1;
  cloud->is_dense = true;

  // 4. 写出为 PLY 文件
  pcl::io::savePLYFileBinary(ply_path, *cloud);
  std::cout << "PLY 文件已写入: " << ply_path << std::endl << "最终点数: " << cloud->points.size() << std::endl;
}

std::string Simple3DBuilder::generate_openbox(
    double inner_length,
    double inner_width,
    double inner_height,
    double outer_length,
    double outer_width,
    double outer_height,
    double corner_radius,
    size_t cloud_points,
    const std::string& output_dir,
    bool output_stl) {
  // 将毫米转换为米
  inner_length = inner_length / 1000.0;
  inner_width = inner_width / 1000.0;
  inner_height = inner_height / 1000.0;

  outer_length = outer_length / 1000.0;
  outer_width = outer_width / 1000.0;
  outer_height = outer_height / 1000.0;

  corner_radius = corner_radius / 1000.0;
  if (inner_length <= 0 || inner_width <= 0 || inner_height <= 0 || outer_length <= 0 || outer_width <= 0 || outer_height <= 0) {
    throw std::invalid_argument("内框和外框都尺寸必须大于0");
  }
  if (outer_length <= inner_length || outer_width <= inner_width || outer_height <= inner_height) {
    throw std::invalid_argument("外框尺寸必须大于内框尺寸");
  }
  if (corner_radius > 0 && corner_radius > (std::min(inner_width, inner_length))) {
    throw std::invalid_argument("倒角半径必须大于内框长和宽的一半");
  }

  // 创建外部箱体
  TopoDS_Solid outer_box = BRepPrimAPI_MakeBox(outer_length, outer_width, outer_height).Solid();

  // 创建内部空腔
  TopoDS_Solid inner_box = BRepPrimAPI_MakeBox(inner_length, inner_width, inner_height).Solid();

  // 平移内盒至外壳内部
  gp_Trsf translation;
  translation.SetTranslation(gp_Vec((outer_length - inner_length) / 2, (outer_width - inner_width) / 2, outer_height - inner_height));
  TopoDS_Shape inner_box_translated = BRepBuilderAPI_Transform(inner_box, translation).Shape();

  // 使用布尔运算切割成开口盒子
  TopoDS_Shape open_box = BRepAlgoAPI_Cut(outer_box, inner_box_translated).Shape();
  if (open_box.IsNull()) {
    throw std::runtime_error("开口盒几何体无效。");
  }

  // 倒角处理
  if (corner_radius > 0) {
    BRepFilletAPI_MakeFillet fillet(open_box);
    for (TopExp_Explorer exp(open_box, TopAbs_EDGE); exp.More(); exp.Next()) {
      TopoDS_Edge edge = TopoDS::Edge(exp.Current());
      gp_Pnt p1, p2;
      Standard_Real f, l;
      Handle(Geom_Curve) curve = BRep_Tool::Curve(edge, f, l);
      if (curve.IsNull()) continue;
      p1 = curve->Value(f);
      p2 = curve->Value(l);
      gp_Vec dir(p1, p2);

      // 仅对近似竖直的边进行倒角
      if (std::abs(dir.X()) < 1e-6 && std::abs(dir.Y()) < 1e-6 && std::abs(dir.Z()) > 1e-6) {
        try {
          fillet.Add(corner_radius, edge);
        } catch (...) {
          std::cerr << "警告：某个边倒角失败。" << std::endl;
        }
      }
    }
    open_box = fillet.Shape();
    if (open_box.IsNull()) {
      throw std::runtime_error("倒角失败，几何体无效。");
    }
  }

  // 平移至中心点(外框)
  translation.SetTranslation(gp_Vec(-outer_length / 2, -outer_width / 2, -outer_height / 2));
  TopoDS_Shape open_box_translated = BRepBuilderAPI_Transform(open_box, translation).Shape();

  std::filesystem::create_directories(output_dir);

  std::string name = "box_" + std::to_string((int)(inner_length * 1000)) + "x" + std::to_string((int)(inner_width * 1000)) + "x" + std::to_string((int)(inner_height * 1000)) + "_" + std::to_string((int)(outer_length * 1000)) + "x" + std::to_string((int)(outer_width * 1000)) + "x" + std::to_string((int)(outer_height * 1000));
  if (corner_radius > 0) {
    name += "_R" + std::to_string((int)(corner_radius * 1000));
  }

  std::string stl_path = output_dir + "/" + name + ".stl";

  // 网格化几何体 - 使用常量参数
  BRepMesh_IncrementalMesh mesh(open_box_translated, Simple3DBuilder::MESH_LINEAR_DEFLECTION, false, Simple3DBuilder::MESH_ANGULAR_DEFLECTION);
  if (!mesh.IsDone()) {
    throw std::runtime_error("网格生成失败。");
  }

  // 导出 STL
  if (output_stl) {
    StlAPI_Writer stl_writer;
    stl_writer.Write(open_box_translated, stl_path.c_str());
    std::cout << "STL 文件已写入：" << stl_path << std::endl;
  }
  std::string ply_path = output_dir + "/" + name + ".ply";
  Simple3DBuilder::sample_shape_surface_to_ply(open_box_translated, ply_path, cloud_points);

  return ply_path;
}

void Simple3DBuilder::sample_stl_to_ply(const std::string& stl_path, const std::string& ply_path, int target_total_points, double scale) {
  TopoDS_Shape shape;
  StlAPI_Reader reader;

  if (!std::filesystem::exists(stl_path)) {
    throw std::invalid_argument("STL 文件不存在: " + stl_path);
  }

  reader.Read(shape, stl_path.c_str());
  if (shape.IsNull()) {
    throw std::runtime_error("无法从 STL 文件读取形状：" + stl_path);
  }

  // 应用缩放变换（如果scale不等于1.0）
  if (std::abs(scale - 1.0) > 1e-9) {
    gp_Trsf scaleTrsf;
    scaleTrsf.SetScale(gp_Pnt(0, 0, 0), scale);
    BRepBuilderAPI_Transform scaleTransform(shape, scaleTrsf);
    shape = scaleTransform.Shape();

    std::cout << "应用缩放因子: " << scale << std::endl;
  }

  // 网格化几何体 - 使用常量参数
  BRepMesh_IncrementalMesh mesh(shape, Simple3DBuilder::MESH_LINEAR_DEFLECTION, false, Simple3DBuilder::MESH_ANGULAR_DEFLECTION);
  if (!mesh.IsDone()) {
    throw std::runtime_error("网格生成失败。");
  }

  Simple3DBuilder::sample_shape_surface_to_ply(shape, ply_path, target_total_points);
}

std::string Simple3DBuilder::generate_robot_tool(
    int tool_type, double tool_diameter, double tool_length, double tip_length,
    int flange_type, double flange_diameter, double flange_height, double bolt_circle, int bolt_number,
    size_t cloud_points, const std::string& output_dir, bool output_stl) {

    std::filesystem::create_directories(output_dir);
    std::stringstream ss;
    ss << "robot_tool_type" << tool_type << "_d" << std::to_string((int)(tool_diameter)) << "_l" << std::to_string((int)(tool_length));
    if (tip_length > 0) ss << "_tip" << std::to_string((int)(tip_length));
    ss << "_flange" << flange_type;

    std::string base_filename = ss.str();
    std::string ply_path = output_dir + "/" + base_filename + ".ply";
    std::string stl_path = output_dir + "/" + base_filename + ".stl";

    // 法兰参数结构
    struct FlangeParams {
        double diameter;
        double height;
        double bolt_circle;
        int bolt_number;
        double bolt_radius;
        double center_hole_radius;
        double center_hole_length;
        double angle_offset;
    };

    FlangeParams params;

    switch (flange_type) {
        case 1: // KR3-KR10
            params = {
                40.0,  // 法兰外径
                15.0,  // 法兰高度
                31.5,  // 螺栓直径
                8,     // 螺栓数量
                2.75,  // M5螺栓孔半径
                10.0,  // 中心定位孔半径
                6.0,   // 中心定位孔长度
                M_PI/4 // 45度偏移
            };
            break;
        case 2: // KR12R1450
            params = {
                65.0,   // 法兰外径
                15.0,   // 法兰高度
                58,     // 螺栓直径
                11,     // 螺栓数量
                2,      // M4螺栓孔半径
                25.5,   // 中心定位孔半径
                6.0,    // 中心定位孔长度
                M_PI/6  // 45度偏移
            };
            break;
        case 3: // KR16-KR22
            params = {
                63.0,  // 法兰外径
                15.0,  // 法兰高度
                50.0,  // 螺栓圆直径
                8,     // 螺栓数量
                3,     // 螺栓孔半径
                15.75, // 中心定位孔半径
                6.0,   // 中心定位孔长度
                M_PI/4 // 45度偏移
            };
            break;
        case 0: // 自定义法兰
            params = {
                flange_diameter,
                flange_height,
                bolt_circle,
                bolt_number,
                4.0,   // 默认螺栓孔半径
                6.0,   // 中心定位孔长度
                flange_diameter * 0.15, // 中心定位孔，直径为法兰直径的15%
                0.0    // 无偏移
            };
            break;
        default:
            throw std::invalid_argument("不支持的法兰类型");
    }

    // 使用标准法兰参数覆盖输入参数
    if (flange_type > 0) {
        flange_diameter = params.diameter;
        flange_height = params.height;
        bolt_circle = params.bolt_circle;
        bolt_number = params.bolt_number;
    }

    // 创建法兰基本圆柱体
    gp_Ax2 flange_axis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
    TopoDS_Shape flange = BRepPrimAPI_MakeCylinder(flange_axis, params.diameter/2, params.height).Shape();

    // 添加螺栓孔
    for (int i = 0; i < params.bolt_number; i++) {
        double angle = 2 * M_PI * i / params.bolt_number + params.angle_offset;
        double x = (params.bolt_circle/2) * cos(angle);
        double y = (params.bolt_circle/2) * sin(angle);
        gp_Ax2 bolt_axis(gp_Pnt(x, y, 0), gp_Dir(0, 0, 1));
        TopoDS_Shape bolt_hole = BRepPrimAPI_MakeCylinder(bolt_axis, params.bolt_radius, params.height).Shape();
        flange = BRepAlgoAPI_Cut(flange, bolt_hole).Shape();
    }

    // 添加中心孔
    gp_Ax2 center_axis(gp_Pnt(0, 0, 0), gp_Dir(0, 0, 1));
    TopoDS_Shape center_hole = BRepPrimAPI_MakeCylinder(center_axis, params.center_hole_radius, params.center_hole_length).Shape();
    flange = BRepAlgoAPI_Cut(flange, center_hole).Shape();

    // 调整工具长度，减去法兰高度
    double actual_tool_length = tool_length - params.height + params.center_hole_length;
    if (actual_tool_length <= 0) {
        throw std::invalid_argument("工具长度必须大于法兰高度");
    }

    // 创建工具部分
    TopoDS_Shape tool;

    switch (tool_type) {
        case 1: { // 针头工具
            // 如果未指定针尖长度，则默认为工具长度的20%
            if (tip_length <= 0) {
                tip_length = actual_tool_length * 0.2;
            } else if (tip_length >= actual_tool_length) {
                throw std::invalid_argument("针尖长度必须小于工具总长度减去法兰高度");
            }

            // 创建针杆
            double shaft_length = actual_tool_length - tip_length;
            gp_Ax2 shaft_axis(gp_Pnt(0, 0, params.height), gp_Dir(0, 0, 1));
            TopoDS_Shape shaft = BRepPrimAPI_MakeCylinder(shaft_axis, tool_diameter/2, shaft_length).Shape();

            // 创建针尖 - 使用更精细的网格参数确保针尖有足够的采样点
            gp_Ax2 tip_axis(gp_Pnt(0, 0, params.height + shaft_length), gp_Dir(0, 0, 1));
            TopoDS_Shape tip = BRepPrimAPI_MakeCone(tip_axis, tool_diameter/2, 0.1, tip_length).Shape(); // 尖端不要完全为0

            // 组合针杆和针尖
            tool = BRepAlgoAPI_Fuse(shaft, tip).Shape();
            break;
        }
        case 2: { // 吸盘工具
            // 如果未指定吸盘高度，则默认为工具长度的30%
            if (tip_length <= 0) {
                tip_length = actual_tool_length * 0.3;
            } else if (tip_length >= actual_tool_length) {
                throw std::invalid_argument("吸盘高度必须小于工具总长度减去法兰高度");
            }

            // 创建吸盘杆
            double shaft_length = actual_tool_length - tip_length;
            gp_Ax2 shaft_axis(gp_Pnt(0, 0, params.height), gp_Dir(0, 0, 1));
            // 使用中心孔直径作为吸盘杆直径
            TopoDS_Shape shaft = BRepPrimAPI_MakeCylinder(shaft_axis, params.center_hole_radius, shaft_length).Shape();

            // 创建吸盘
            double cup_outer_radius = tool_diameter/2;
            double cup_inner_radius = cup_outer_radius * 0.8;
            gp_Ax2 cup_axis(gp_Pnt(0, 0, params.height + shaft_length), gp_Dir(0, 0, 1));
            TopoDS_Shape cup_outer = BRepPrimAPI_MakeCylinder(cup_axis, cup_outer_radius, tip_length).Shape();
            gp_Ax2 cup_inner_axis(gp_Pnt(0, 0, params.height + shaft_length + tip_length*0.2), gp_Dir(0, 0, 1));
            TopoDS_Shape cup_inner = BRepPrimAPI_MakeCylinder(cup_inner_axis, cup_inner_radius, tip_length*0.8).Shape();
            TopoDS_Shape cup = BRepAlgoAPI_Cut(cup_outer, cup_inner).Shape();

            // 组合吸盘杆和吸盘
            tool = BRepAlgoAPI_Fuse(shaft, cup).Shape();
            break;
        }
        case 3: { // 夹爪工具
            // 如果未指定夹爪臂长度，则默认为工具长度的70%
            if (tip_length <= 0) {
                tip_length = actual_tool_length * 0.7;
            } else if (tip_length >= actual_tool_length) {
                throw std::invalid_argument("夹爪臂长度必须小于工具总长度减去法兰高度");
            }

            // 创建夹爪基座
            double base_height = actual_tool_length - tip_length;
            gp_Ax2 base_axis(gp_Pnt(0, 0, params.height), gp_Dir(0, 0, 1));
            TopoDS_Shape base = BRepPrimAPI_MakeCylinder(base_axis, tool_diameter*0.6/2, base_height).Shape();

            // 创建夹爪臂
            double arm_width = tool_diameter * 0.2;
            double arm_thickness = tool_diameter * 0.1;
            double arm_spacing = tool_diameter * 0.3;

            // 左臂
            TopoDS_Shape left_arm = BRepPrimAPI_MakeBox(
                gp_Pnt(-arm_width/2, -arm_spacing/2 - arm_thickness, params.height + base_height),
                arm_width, arm_thickness, tip_length).Shape();

            // 右臂
            TopoDS_Shape right_arm = BRepPrimAPI_MakeBox(
                gp_Pnt(-arm_width/2, arm_spacing/2, params.height + base_height),
                arm_width, arm_thickness, tip_length).Shape();

            // 组合基座和夹爪臂
            tool = BRepAlgoAPI_Fuse(base, left_arm).Shape();
            tool = BRepAlgoAPI_Fuse(tool, right_arm).Shape();
            break;
        }
        default:
            throw std::invalid_argument("不支持的工具类型");
    }

    // 组合法兰和工具
    TopoDS_Shape complete_tool = BRepAlgoAPI_Fuse(flange, tool).Shape();

    // 平移模型，使法兰中心位于原点
    gp_Trsf translation;
    translation.SetTranslation(gp_Vec(0, 0, -params.center_hole_length));
    BRepBuilderAPI_Transform transform(complete_tool, translation);
    complete_tool = transform.Shape();

    gp_Trsf scaleTrsf;
    scaleTrsf.SetScale(gp_Pnt(0, 0, 0), 0.001);
    BRepBuilderAPI_Transform scaleTransform(complete_tool, scaleTrsf);
    complete_tool = scaleTransform.Shape();

    // 网格化几何体 - 使用常量参数确保针尖等小细节被正确捕获
    BRepMesh_IncrementalMesh mesh(complete_tool, Simple3DBuilder::MESH_LINEAR_DEFLECTION, false, Simple3DBuilder::MESH_ANGULAR_DEFLECTION);
    if (!mesh.IsDone()) {
      throw std::runtime_error("网格生成失败。");
    }

    // 导出 STL
    if (output_stl) {
      StlAPI_Writer stl_writer;
      stl_writer.Write(complete_tool, stl_path.c_str());
      std::cout << "STL 文件已写入: " << stl_path << std::endl;
    }

    // 导出 PLY
    Simple3DBuilder::sample_shape_surface_to_ply(complete_tool, ply_path, cloud_points);

    return ply_path;
}
