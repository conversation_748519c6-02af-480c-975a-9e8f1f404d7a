#include <iostream>
#include <getopt.h>
#include <string>
#include <stdexcept>
#include "Simple3DBuilder.h"

void print_usage() {
    std::cout << "用法: ./ToolGenerator [选项]\n"
              << "选项:\n"
              << "  -t, --tool-type <value>      工具类型 (1=针头, 2=吸盘, 3=夹爪) (必填)\n"
              << "  -d, --tool-diameter <value>  工具直径，单位毫米 (必填)\n"
              << "  -l, --tool-length <value>    工具长度，单位毫米 (必填)\n"
              << "  -p, --tip-length <value>     工具末端长度(针尖|吸盘|夹臂)，单位毫米 (必填)\n"
              << "  -f, --flange-type <value>    法兰类型 (1=KR3-KR10, 2=KR12R1450, 3=KR16-KR22, 0=自定义) (必填)\n"
              << "  -r, --flange-diameter <value> 法兰直径，单位毫米 (当法兰类型为0时必填)\n"
              << "  -h, --flange-height <value>   法兰高度，单位毫米 (当法兰类型为0时必填)\n"
              << "  -b, --bolt-circle <value>     法兰螺栓圆直径，单位毫米 (当法兰类型为0时必填)\n"
              << "  -c, --bolt-number <value>     法兰螺栓数量 (当法兰类型为0时必填)\n"
              << "      --bolt-radius <value>     螺栓孔半径，单位毫米 (当法兰类型为0时可选, 默认: 2.75)\n"
              << "      --center-hole-radius <value> 中心定位孔半径，单位毫米 (当法兰类型为0时可选, 默认: 法兰直径*0.15)\n"
              << "      --center-hole-length <value> 中心定位孔长度，单位毫米 (当法兰类型为0时可选, 默认: 6.0)\n"
              << "      --angle-offset <value>    角度偏移，单位弧度 (当法兰类型为0时可选, 默认: 0.0)\n"
              << "  -n, --cloud-points <value>    PLY点云数量，即STL模型采样点个数 (可选 默认: 100000)\n"
              << "  -o, --output-dir <path>       模型文件输出目录 (可选, 默认: 当前工作目录)\n"
              << "  -s, --output-stl <value>      是否输出STL文件 (0=否, 1=是) (可选, 默认: 0)\n";
}

int main(int argc, char* argv[]) {
    int tool_type = 0;
    double tool_diameter = 0, tool_length = 0, tip_length = 0;
    int flange_type = 0;
    double flange_diameter = 0, flange_height = 0, bolt_circle = 0;
    int bolt_number = 0;
    double bolt_radius = 2.75;  // 默认M5螺栓孔半径
    double center_hole_radius = 0;  // 默认为法兰直径的15%，在后面计算
    double center_hole_length = 6.0;  // 默认中心定位孔长度
    double angle_offset = 0.0;  // 默认无偏移
    size_t cloud_points = 100000;
    std::string output_dir = ".";
    bool output_stl = false;

    const struct option long_options[] = {
        {"tool-type", required_argument, nullptr, 't'},
        {"tool-diameter", required_argument, nullptr, 'd'},
        {"tool-length", required_argument, nullptr, 'l'},
        {"tip-length", required_argument, nullptr, 'p'},
        {"flange-type", required_argument, nullptr, 'f'},
        {"flange-diameter", required_argument, nullptr, 'r'},
        {"flange-height", required_argument, nullptr, 'h'},
        {"bolt-circle", required_argument, nullptr, 'b'},
        {"bolt-number", required_argument, nullptr, 'c'},
        {"bolt-radius", required_argument, nullptr, 1001},
        {"center-hole-radius", required_argument, nullptr, 1002},
        {"center-hole-length", required_argument, nullptr, 1003},
        {"angle-offset", required_argument, nullptr, 1004},
        {"cloud-points", required_argument, nullptr, 'n'},
        {"output-dir", required_argument, nullptr, 'o'},
        {"output-stl", required_argument, nullptr, 's'},
        {nullptr, 0, nullptr, 0}
    };

    int opt;
    while ((opt = getopt_long(argc, argv, "t:d:l:p:f:r:h:b:c:n:o:s:", long_options, nullptr)) != -1) {
        try {
            switch (opt) {
                case 't': tool_type = std::stoi(optarg); break;
                case 'd': tool_diameter = std::stod(optarg); break;
                case 'l': tool_length = std::stod(optarg); break;
                case 'p': tip_length = std::stod(optarg); break;
                case 'f': flange_type = std::stoi(optarg); break;
                case 'r': flange_diameter = std::stod(optarg); break;
                case 'h': flange_height = std::stod(optarg); break;
                case 'b': bolt_circle = std::stod(optarg); break;
                case 'c': bolt_number = std::stoi(optarg); break;
                case 1001: bolt_radius = std::stod(optarg); break;
                case 1002: center_hole_radius = std::stod(optarg); break;
                case 1003: center_hole_length = std::stod(optarg); break;
                case 1004: angle_offset = std::stod(optarg); break;
                case 'n': cloud_points = std::stoi(optarg); break;
                case 'o': output_dir = optarg ? optarg : "."; break;
                case 's': output_stl = std::stoi(optarg); break;
                default:
                    print_usage();
                    return 1;
            }
        } catch (const std::invalid_argument&) {
            std::cerr << "参数值无效，请输入数字。" << std::endl;
            return 1;
        }
    }

    // 参数验证
    if (tool_type <= 0 || tool_type > 3 || tool_diameter <= 0 || tool_length <= 0 || flange_type < 0  || cloud_points <= 0) {
        print_usage();
        return 1;
    }

    // 自定义法兰需要额外参数
    if (flange_type == 0 && (flange_diameter <= 0 || flange_height <= 0 || bolt_circle <= 0 || bolt_number <= 0)) {
        std::cerr << "错误：自定义法兰类型需要指定法兰直径、高度、螺栓圆直径和螺栓数量。" << std::endl;
        print_usage();
        return 1;
    }

    // 如果center_hole_radius未设置，使用默认值（法兰直径的15%）
    if (flange_type == 0 && center_hole_radius == 0) {
        center_hole_radius = flange_diameter * 0.15;
    }

    try {
        auto file = Simple3DBuilder::generate_robot_tool(
            tool_type, tool_diameter, tool_length, tip_length,
            flange_type, flange_diameter, flange_height, bolt_circle, bolt_number,
            bolt_radius, center_hole_radius, center_hole_length, angle_offset,
            cloud_points, output_dir, output_stl);
    } catch (const std::exception& e) {
        std::cerr << "错误：" << e.what() << std::endl;
        return 1;
    }

    return 0;
}
